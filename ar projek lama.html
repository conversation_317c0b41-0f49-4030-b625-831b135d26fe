<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Combined AR App</title>
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v7.0.0/dist/aframe-extras.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.2.5/dist/mindar-image-aframe.prod.js"></script>
    <style>
      .a-enter-vr {
        display: none !important;
      }

      /* Startup Screen Styles */
      #startup-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: 'Arial', sans-serif;
      }

      #startup-screen.hidden {
        display: none;
      }

      .logo-container {
        margin-bottom: 50px;
      }

      .logo-text {
        font-size: 48px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
      }

      /* Artivive-style Loading Animation */
      .loading-container {
        margin-bottom: 50px;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #333;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        font-size: 18px;
        color: #666;
        text-align: center;
        animation: pulse 2s ease-in-out infinite;
      }

      @keyframes pulse {
        0%, 100% { opacity: 0.6; }
        50% { opacity: 1; }
      }

      /* Start Button */
      #start-button {
        padding: 20px 60px;
        font-size: 24px;
        font-weight: bold;
        background-color: #333;
        color: white;
        border: none;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        display: none;
      }

      #start-button:hover {
        background-color: #555;
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
      }

      #start-button:active {
        transform: translateY(0);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
      }

      #start-button.show {
        display: block;
        animation: fadeInUp 0.5s ease-out;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      #toggle-button {
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        padding: 20px 50px;
        font-size: 16px;
        background-color: white;
        color: black;
        box-shadow: 0px 10px 13px -7px #000000,
          5px 5px 15px 5px rgba(0, 0, 0, 0);
        border: 5px solid transparent;
        border-radius: 27px;
        transition: background-color 0.2s ease, color 0.2s ease;
      }

      #toggle-button:hover {
        background-color: #f0f0f0;
      }

      #toggle-button:active {
        background-color: #cad5d9;
        color: white;
        transform: translateX(-50%) scale(0.96);
      }

      .ar-link {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #4caf50;
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-family: Arial, sans-serif;
        font-size: 18px;
        display: none;
        z-index: 1000;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .ar-link:hover {
        background-color: #45a049;
      }

      .target-text {
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .tv-frame {
        position: absolute;
        width: 1.1;
        height: 0.6;
        background-color: #333;
        border-radius: 0.1;
        z-index: -1;
      }

      .tv-stand {
        position: absolute;
        width: 0.2;
        height: 0.1;
        background-color: #333;
        bottom: -0.1;
        left: 0.45;
      }

      .video-container {
        position: absolute;
        width: 1;
        height: 0.5;
        overflow: hidden;
      }
    </style>
  </head>

  <body style="margin: 0; overflow: hidden">
    <!-- Startup Screen -->
    <div id="startup-screen">
      <div class="logo-container">
        <div class="logo-text">AR Experience</div>
      </div>

      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading AR Experience...</div>
      </div>

      <button id="start-button" onclick="startARExperience()">Start AR</button>
    </div>

    <button id="toggle-button" onclick="toggleInfo()">Info</button>
    <a href="#" class="ar-link" id="websiteLink">Visit Website</a>

    <video
      id="ar-video"
      src="https://cdn.glitch.me/7a663fb2-a522-47b6-9e6a-f3acfe1d5a8f/RE_compiled%20animated.mp4?v=1747279561796"
      loop
      autoplay
      muted
      playsinline
      style="display: none"
    ></video>

    <a-scene
      mindar-image="imageTargetSrc: https://cdn.glitch.global/7a663fb2-a522-47b6-9e6a-f3acfe1d5a8f/targets.mind?v=1747280453573;
                    uiScanning: no;
                    filterMinCF: 0.0001;
                    filterBeta: 0.001;
                    missTolerance: 5;
                    warmupTolerance: 3;"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
      renderer="precision: high; antialias: true;"
      style="display: none;"
      id="ar-scene"
    >
      <a-camera position="0 0 0" look-controls="enabled: false"></a-camera>

      <!-- Image Target 0 -->
      <a-entity mindar-image-target="targetIndex: 0" id="target-0">
        <a-entity class="tv-frame" position="0 0 -0.01">
          <a-entity class="tv-stand"></a-entity>
        </a-entity>
        <a-entity class="video-container" position="0 0 0">
          <a-video
            id="video-plane"
            src="https://cdn.glitch.me/7a663fb2-a522-47b6-9e6a-f3acfe1d5a8f/RE_compiled%20animated.mp4?v=1747279561796"
            position="0 0 0"
            width="1"
            height="0.45"
            scale="1 1 1"
            visible="true"
          ></a-video>
        </a-entity>
        <a-text
          class="target-text"
          value="Link A"
          color="white"
          position="0 0.6 0"
          scale="0.5 0.5 0.5"
        ></a-text>
      </a-entity>

      <!-- Image Target 1 - Duplicate video + text -->
      <a-entity mindar-image-target="targetIndex: 1" id="target-1">
        <a-entity class="tv-frame" position="0 0 -0.01">
          <a-entity class="tv-stand"></a-entity>
        </a-entity>
        <a-entity class="video-conta iner" position="0 0 0">
          <a-video
            src="https://cdn.glitch.me/7a663fb2-a522-47b6-9e6a-f3acfe1d5a8f/RE_compiled%20animated.mp4?v=1747279561796"
            position="0 0.05 0"
            width="1"
            height="0.45"
            scale="1 1 1"
            visible="true"
          ></a-video>
        </a-entity>
        <a-text
          class="target-text"
          value="Link A"
          color="white"
          position="0 0.6 0"
          scale="0.5 0.5 0.5"
        ></a-text>
      </a-entity>
    </a-scene>

    <script>
      let isInfoShown = false;
      const websiteLink = document.getElementById("websiteLink");
      const video = document.getElementById("ar-video");
      const videoPlane = document.getElementById("video-plane");
      const targetTexts = document.querySelectorAll(".target-text");
      const startupScreen = document.getElementById("startup-screen");
      const startButton = document.getElementById("start-button");
      const arScene = document.getElementById("ar-scene");

      let currentTarget = null;
      let isARStarted = false;

      // Startup screen functionality
      function showStartButton() {
        setTimeout(() => {
          startButton.classList.add("show");
        }, 2000); // Show start button after 2 seconds of loading
      }

      function startARExperience() {
        startupScreen.classList.add("hidden");
        arScene.style.display = "block";
        isARStarted = true;
        initializeCamera();
      }

      // Initialize the startup sequence
      document.addEventListener("DOMContentLoaded", () => {
        showStartButton();
      });

      function hideAllTexts() {
        targetTexts.forEach(text => {
          text.setAttribute("opacity", "0");
        });
      }

      function toggleInfo() {
        if (!currentTarget) return;

        isInfoShown = !isInfoShown;

        if (isInfoShown) {
          video.pause();
          document.querySelectorAll("a-video").forEach(v => v.setAttribute("visible", false));
          websiteLink.href = "https://www.youtube.com/";
          websiteLink.textContent = "Visit YouTube";
          websiteLink.style.display = "block";
          document.querySelector(`#${currentTarget} .target-text`).setAttribute("opacity", "1");
        } else {
          video.play();
          document.querySelectorAll("a-video").forEach(v => v.setAttribute("visible", true));
          websiteLink.style.display = "none";
          hideAllTexts();
        }
      }

      const allTargets = ["target-0", "target-1"];
      allTargets.forEach(id => {
        const target = document.getElementById(id);
        target.addEventListener("targetFound", () => {
          currentTarget = id;
          if (!isInfoShown) {
            video.play();
            document.querySelector(`#${id} .target-text`).setAttribute("opacity", "0");
          } else {
            document.querySelector(`#${id} .target-text`).setAttribute("opacity", "1");
          }
        });

        target.addEventListener("targetLost", () => {
          currentTarget = null;
          video.pause();
          websiteLink.style.display = "none";
          hideAllTexts();
        });
      });

      async function initializeCamera() {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode: "environment", width: { ideal: 1920 }, height: { ideal: 1080 } },
          });
          video.srcObject = stream;
          video.setAttribute("playsinline", true);
          video.play();
        } catch (err) {
          console.warn("Camera access failed:", err);
        }
      }
    </script>
  </body>
</html>