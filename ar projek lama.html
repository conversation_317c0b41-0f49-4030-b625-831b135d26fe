<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Combined AR App</title>
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v7.0.0/dist/aframe-extras.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.2.5/dist/mindar-image-aframe.prod.js"></script>
    <style>
      .a-enter-vr {
        display: none !important;
      }

      #toggle-button {
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        padding: 20px 50px;
        font-size: 16px;
        background-color: white;
        color: black;
        box-shadow: 0px 10px 13px -7px #000000,
          5px 5px 15px 5px rgba(0, 0, 0, 0);
        border: 5px solid transparent;
        border-radius: 27px;
        transition: background-color 0.2s ease, color 0.2s ease;
      }

      #toggle-button:hover {
        background-color: #f0f0f0;
      }

      #toggle-button:active {
        background-color: #cad5d9;
        color: white;
        transform: translateX(-50%) scale(0.96);
      }

      .ar-link {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #4caf50;
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-family: Arial, sans-serif;
        font-size: 18px;
        display: none;
        z-index: 1000;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .ar-link:hover {
        background-color: #45a049;
      }

      .target-text {
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .tv-frame {
        position: absolute;
        width: 1.1;
        height: 0.6;
        background-color: #333;
        border-radius: 0.1;
        z-index: -1;
      }

      .tv-stand {
        position: absolute;
        width: 0.2;
        height: 0.1;
        background-color: #333;
        bottom: -0.1;
        left: 0.45;
      }

      .video-container {
        position: absolute;
        width: 1;
        height: 0.5;
        overflow: hidden;
      }
    </style>
  </head>

  <body style="margin: 0; overflow: hidden">
    <button id="toggle-button" onclick="toggleInfo()">Info</button>
    <a href="#" class="ar-link" id="websiteLink">Visit Website</a>

    <video
      id="ar-video"
      src="https://driyanyy.github.io/project/WhatsApp%20Video%202025-05-20%20at%2010.08.10_53f8c34b.mp4"
      loop
      autoplay
      muted
      playsinline
      style="display: none"
    ></video>

    <a-scene
      mindar-image="imageTargetSrc: https://driyanyy.github.io/project/targets.mind; 
                    uiScanning: no; 
                    filterMinCF: 0.0001; 
                    filterBeta: 0.001; 
                    missTolerance: 5; 
                    warmupTolerance: 3;"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
      renderer="precision: high; antialias: true;"
    >
      <a-camera position="0 0 0" look-controls="enabled: false"></a-camera>

      <!-- Image Target 0 -->
      <a-entity mindar-image-target="targetIndex: 0" id="target-0">
        <a-entity class="tv-frame" position="0 0 -0.01">
          <a-entity class="tv-stand"></a-entity>
        </a-entity>
        <a-entity class="video-container" position="0 0 0">
          <a-video
            id="video-plane"
            src="https://driyanyy.github.io/project/WhatsApp%20Video%202025-05-20%20at%2010.08.10_53f8c34b.mp4"
            position="0 0 0"
            width="1"
            height="0.45"
            scale="1 1 1"
            visible="true"
          ></a-video>
        </a-entity>
        <a-text
          class="target-text"
          value="Link A"
          color="white"
          position="0 0.6 0"
          scale="0.5 0.5 0.5"
        ></a-text>
      </a-entity>

      <!-- Image Target 1 - Duplicate video + text -->
      <a-entity mindar-image-target="targetIndex: 1" id="target-1">
        <a-entity class="tv-frame" position="0 0 -0.01">
          <a-entity class="tv-stand"></a-entity>
        </a-entity>
        <a-entity class="video-container" position="0 0 0">
          <a-video
            src="https://driyanyy.github.io/project/WhatsApp%20Video%202025-05-20%20at%2010.08.10_53f8c34b.mp4"
            position="0 0.05 0"
            width="1"
            height="0.45"
            scale="1 1 1"
            visible="true"
          ></a-video>
        </a-entity>
        <a-text
          class="target-text"
          value="Link A"
          color="white"
          position="0 0.6 0"
          scale="0.5 0.5 0.5"
        ></a-text>
      </a-entity>
    </a-scene>

    <script>
      let isInfoShown = false;
      const websiteLink = document.getElementById("websiteLink");
      const video = document.getElementById("ar-video");
      const videoPlane = document.getElementById("video-plane");
      const targetTexts = document.querySelectorAll(".target-text");

      let currentTarget = null;

      function hideAllTexts() {
        targetTexts.forEach(text => {
          text.setAttribute("opacity", "0");
        });
      }

      function toggleInfo() {
        if (!currentTarget) return;

        isInfoShown = !isInfoShown;

        if (isInfoShown) {
          video.pause();
          document.querySelectorAll("a-video").forEach(v => v.setAttribute("visible", false));
          websiteLink.href = "https://www.youtube.com/";
          websiteLink.textContent = "Visit YouTube";
          websiteLink.style.display = "block";
          document.querySelector(`#${currentTarget} .target-text`).setAttribute("opacity", "1");
        } else {
          video.play();
          document.querySelectorAll("a-video").forEach(v => v.setAttribute("visible", true));
          websiteLink.style.display = "none";
          hideAllTexts();
        }
      }

      const allTargets = ["target-0", "target-1"];
      allTargets.forEach(id => {
        const target = document.getElementById(id);
        target.addEventListener("targetFound", () => {
          currentTarget = id;
          if (!isInfoShown) {
            video.play();
            document.querySelector(`#${id} .target-text`).setAttribute("opacity", "0");
          } else {
            document.querySelector(`#${id} .target-text`).setAttribute("opacity", "1");
          }
        });

        target.addEventListener("targetLost", () => {
          currentTarget = null;
          video.pause();
          websiteLink.style.display = "none";
          hideAllTexts();
        });
      });

      document.addEventListener("DOMContentLoaded", async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode: "environment", width: { ideal: 1920 }, height: { ideal: 1080 } },
          });
          video.srcObject = stream;
          video.setAttribute("playsinline", true);
          video.play();
        } catch (err) {
          console.warn("Camera access failed:", err);
        }
      });
    </script>
  </body>
</html>